'use client';
import parse from 'html-react-parser';
import convert from '@/lib/helpers';
import CopyToClipboardButton from '@/components/copy';
import { useCorrectionStore } from '@/context/correction';
import type { EOTask, ResultatEE } from '@/types';
import { useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'sonner';
import { BUCKET_BASE_EO_URL, BUCKET_BASE_URL } from '@/config';
import { Button } from '@/components/ui/button';
import TipTapEditor from '@/components/myEditor';
import { Loader2, Send } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import logger from '@/lib/logger';

interface EOTasksProps {
  result: ResultatEE;
}
export const EOTasks = ({ result }: EOTasksProps) => {
  const currentTask = useCorrectionStore((state) => state.currentTask);
  const setSerieId = useCorrectionStore((state) => state.setSerieId);
  const setUserId = useCorrectionStore((state) => state.setUserId);
  const reset = useCorrectionStore((state) => state.reset);
  useEffect(() => {
    setSerieId(result.serie._id);
    setUserId(result.user._id);
    return () => {
      // Reset All when component unmounts
      reset();
    };
  }, [result, setSerieId, reset, setUserId]);
  return (
    <>
      {currentTask == 1 && (
        <TaskBody
          payload={result.payload}
          taskNumber={1}
          task={
            result.serie.eoQuestions[0].tasks.find(
              (t) => t.numero == 82,
            ) as EOTask
          }
        />
      )}

      {currentTask == 2 && (
        <TaskBody
          payload={result.payload}
          taskNumber={2}
          task={
            result.serie.eoQuestions[0].tasks.find(
              (t) => t.numero == 83,
            ) as EOTask
          }
        />
      )}

      {currentTask == 3 && (
        <TaskBody
          payload={result.payload}
          taskNumber={3}
          task={
            result.serie.eoQuestions[0].tasks.find(
              (t) => t.numero == 84,
            ) as EOTask
          }
        />
      )}
    </>
  );
};

interface Payload {
  taskUrl1: string;
  taskUrl2: string;
  taskUrl3: string;
}
const TaskBody = ({
  payload,
  taskNumber,
  task,
}: {
  payload: string;
  taskNumber: number;
  task: EOTask;
}) => {
  const audioConsigneRef = useRef<HTMLAudioElement>(null);

  const [showComment, setShowComment] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  //note
  const setNote = useCorrectionStore((state) => state.setNoteTask);
  const noteTaskOne = useCorrectionStore((state) => state.noteTaskOne);
  const noteTaskTwo = useCorrectionStore((state) => state.noteTaskTwo);
  const noteTaskThree = useCorrectionStore((state) => state.noteTaskThree);
  //comment
  const setComment = useCorrectionStore((state) => state.setCommentTask);
  const commentTaskOne = useCorrectionStore((state) => state.commentTaskOne);
  const commentTaskTwo = useCorrectionStore((state) => state.commentTaskTwo);
  const commentTaskThree = useCorrectionStore(
    (state) => state.commentTaskThree,
  );

  const comment = useMemo(() => {
    switch (taskNumber) {
      case 1:
        return commentTaskOne;
      case 2:
        return commentTaskTwo;
      case 3:
        return commentTaskThree;
      default:
        return '';
    }
  }, [taskNumber, commentTaskOne, commentTaskTwo, commentTaskThree]);

  const note = useMemo(() => {
    switch (taskNumber) {
      case 1:
        return noteTaskOne;
      case 2:
        return noteTaskTwo;
      case 3:
        return noteTaskThree;
      default:
        return 0;
    }
  }, [taskNumber, noteTaskOne, noteTaskTwo, noteTaskThree]);

  const extractedPayload = useMemo(
    () => JSON.parse(payload) as Payload,
    [payload],
  );

  const maxNote = useMemo(() => {
    switch (taskNumber) {
      case 1:
        return 5;
      case 2:
        return 8;
      case 3:
        return 7;
      default:
        return 0;
    }
  }, [taskNumber]);

  const userResponseUrl = useMemo(() => {
    switch (taskNumber) {
      case 1:
        return extractedPayload.taskUrl1;
      case 2:
        return extractedPayload.taskUrl2;
      case 3:
        return extractedPayload.taskUrl3;
      default:
        return '';
    }
  }, [taskNumber, extractedPayload]);

  const convertedConsigne = useMemo(
    () => convert(task.consigne).trim(),
    [task.consigne],
  );
  const parsedConsigne = useMemo(() => parse(task.consigne), [task.consigne]);
  return (
    <div className="grid items-start gap-10 lg:grid-cols-[1.75fr_2fr]">
      <div className="flex items-center justify-center rounded-sm border border-gray-800 bg-blue-300 p-2">
        <div className="prose flex min-w-full flex-col rounded-sm border border-gray-700 bg-white p-3 text-justify font-normal">
          {parsedConsigne}
          <audio
            ref={audioConsigneRef}
            className="hidden"
            src={`${BUCKET_BASE_URL}/${task.fichier}`}
            onEnded={() => {
              setIsPlaying(false);
            }}
            onError={() => {
              toast.warning(
                "Impossible de charger l'audio, lisez la description de la tâche.",
                { duration: 5000, position: 'bottom-right' },
              );
            }}
          />
          <Button
            onClick={() => {
              if (audioConsigneRef.current) {
                if (isPlaying) {
                  audioConsigneRef.current.pause();
                  setIsPlaying(false);
                } else {
                  audioConsigneRef.current.play().catch(() => {
                    toast.error("Erreur lors de la lecture de l'audio.");
                  });
                  setIsPlaying(true);
                }
              }
            }}
            className="mx-auto my-1 w-fit"
          >
            {isPlaying ? 'Pause' : 'Écouter la consigne'}
          </Button>
          <div className="ml-auto mt-0.5 flex w-fit items-center justify-between rounded-sm border border-gray-500 bg-white p-2">
            <CopyToClipboardButton text={convertedConsigne} />
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-3">
        <div className="rounded-sm border border-gray-800 bg-blue-300 p-2">
          <div
            className={cn(
              'min-w-full rounded-sm border-2 bg-white p-3 text-justify font-normal',
            )}
          >
            <audio
              controls
              className="w-full"
              src={`${BUCKET_BASE_EO_URL}/${userResponseUrl}`}
              onError={() => {
                toast.warning(
                  "Impossible de charger l'audio, actualisez la page.",
                  { duration: 5000, position: 'bottom-right' },
                );
              }}
            />
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <Label
              htmlFor={`note-task-${taskNumber}`}
              className="text-sm font-semibold"
            >
              Attribuez une note (0 à {maxNote})
            </Label>
            <Input
              autoFocus
              id={`note-task-${taskNumber}`}
              name={`note-task-${taskNumber}`}
              type="number"
              className="w-20 text-xl font-medium"
              max={maxNote}
              value={note || ''}
              onInput={(e) => {
                const value = e.currentTarget.value;
                if (value && parseInt(value) > maxNote) {
                  e.currentTarget.value = maxNote.toString();
                  setNote(maxNote);
                } else {
                  setNote(parseInt(value) || 0);
                }
              }}
              onChange={(e) => {
                const value = e.currentTarget.value;
                if (value && parseInt(value) > maxNote) {
                  e.currentTarget.value = maxNote.toString();
                  setNote(maxNote);
                } else {
                  setNote(parseInt(value) || 0);
                }
              }}
            />
          </div>
        </div>
        {!showComment && !comment && (
          <Button
            variant="secondary"
            size={'sm'}
            className="w-fit"
            onClick={() => setShowComment(!showComment)}
          >
            Commenter
          </Button>
        )}
        {(showComment || comment) && (
          <div className="rounded-lg border border-gray-300 bg-white p-4 shadow-md">
            <TipTapEditor content={comment || ''} onChange={setComment} />
          </div>
        )}

        {taskNumber == 3 && (
          <div className="flex items-center justify-end">
            <SendButton payload={payload} />
          </div>
        )}
      </div>
    </div>
  );
};

const SendButton = ({ payload }: { payload: string }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { data: session } = useSession();
  const router = useRouter();
  const { correctionId: testId } = useParams();

  const reset = useCorrectionStore((state) => state.reset);
  const serieId = useCorrectionStore((state) => state.serieId);
  const userId = useCorrectionStore((state) => state.userId);
  //note
  const noteTaskOne = useCorrectionStore((state) => state.noteTaskOne);
  const noteTaskTwo = useCorrectionStore((state) => state.noteTaskTwo);
  const noteTaskThree = useCorrectionStore((state) => state.noteTaskThree);
  //comment
  const commentTaskOne = useCorrectionStore((state) => state.commentTaskOne);
  const commentTaskTwo = useCorrectionStore((state) => state.commentTaskTwo);
  const commentTaskThree = useCorrectionStore(
    (state) => state.commentTaskThree,
  );

  const updateTest = async () => {
    setIsLoading(true);
    if (!noteTaskOne || !noteTaskTwo || !noteTaskThree) {
      toast.error('Vous devez attribuez une note à toutes les tâches.', {
        dismissible: true,
        duration: 1000 * 60 * 2,
        closeButton: true,
      });
      setIsLoading(false);
      return;
    }

    const baseUrl =
      process.env.NEXT_PUBLIC_BASEURL_PROD ||
      process.env.NEXT_PUBLIC_BASEURL ||
      '';

    const PAYLOAD = {
      serie: serieId,
      user: userId,
      resultat: [
        { task: 'tache1', note: noteTaskOne, comment: commentTaskOne },
        { task: 'tache2', note: noteTaskTwo, comment: commentTaskTwo },
        { task: 'tache3', note: noteTaskThree, comment: commentTaskThree },
      ],
      comment: '',
      payload: payload,
      status: 'terminer',
    };
    try {
      const res = await fetch(`${baseUrl}/api/eoTest/tests/${testId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session?.user.accessToken}`,
        },
        body: JSON.stringify(PAYLOAD),
      });
      if (!res.ok) {
        const errorData = await res.json();
        logger.log('Error data:', errorData);

        toast.error(`Une erreur est survenue`, {
          dismissible: true,
          duration: 1000 * 60 * 2,
          closeButton: true,
        });
        return;
      }
      toast.success('Correction envoyée avec succès !', {
        dismissible: true,
        closeButton: true,
      });
      localStorage.removeItem(`correction-${testId}`);
      reset();
      if (session?.user.role == 'superdealer') {
        router.back();
      } else router.push('/dashboard/profiles/correction');
    } catch (error) {
      toast.error(
        "Une erreur est survenue lors de l'envoi de la correction. Veuillez réessayer plus tard.",
        {
          dismissible: true,
          closeButton: true,
        },
      );
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <Button
      className="group flex w-fit items-center gap-2"
      disabled={isLoading}
      onClick={updateTest}
    >
      Envoyer{' '}
      {isLoading ? (
        <Loader2 className="h-4 w-5 animate-spin" />
      ) : (
        <Send className="h-5 w-5 transition-transform duration-1000 group-hover:rotate-45" />
      )}
    </Button>
  );
};
