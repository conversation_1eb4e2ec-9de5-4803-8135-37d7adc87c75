'use client';

import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from '@/components/ui/use-toast';

interface SignInData {
  email: string;
  password: string;
}

interface UseSignInReturn {
  signInUser: (data: SignInData, callbackUrl?: string) => Promise<void>;
  isLoading: boolean;
}

export function useSignIn(): UseSignInReturn {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const signInUser = async (data: SignInData, callbackUrl?: string) => {
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        email: data.email.trim(),
        password: data.password,
        redirect: false,
        callbackUrl: callbackUrl || '/',
      });

      if (result?.error) {
        toast({
          title: 'Erreur de connexion',
          description: result.error,
          variant: 'destructive',
        });
        return;
      }

      if (result?.ok) {
        toast({
          title: 'Connexion réussie',
          description: 'Redirection en cours...',
        });

        // Attendre un peu pour que la session soit mise à jour
        await new Promise((resolve) => setTimeout(resolve, 200));

        // Redirection vers l'URL de callback ou la page d'accueil
        const redirectUrl = callbackUrl || '/';

        // console.log('Redirection vers:', redirectUrl);

        // Essayer d'abord avec router.push
        try {
          router.push(redirectUrl);
          // Si ça ne marche pas après 1 seconde, forcer avec window.location
          setTimeout(() => {
            if (window.location.pathname === '/signin') {
              // console.log('Redirection forcée avec window.location');
              window.location.href = redirectUrl;
            }
          }, 1000);
        } catch (routerError) {
          // console.log(
          //   'Erreur router.push, utilisation de window.location:',
          //   routerError,
          // );
          window.location.href = redirectUrl;
        }
      }
    } catch (error) {
      // console.error('Erreur lors de la connexion:', error);
      toast({
        title: 'Erreur',
        description: "Une erreur inattendue s'est produite",
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    signInUser,
    isLoading,
  };
}
