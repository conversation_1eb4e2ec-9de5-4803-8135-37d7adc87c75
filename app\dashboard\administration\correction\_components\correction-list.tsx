import { ExpressionTest } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';
import axios from 'axios';
import { getAuthSession } from '@/lib/auth';
import ExpressionList from './tables';
const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
async function getTests(token: string, mode: string, examen: string) {
  const { data } = await axios.get<ExpressionTest[]>(
    `${baseUrl}/api/${examen == 'TCF' ? '' : 'tef/'}${mode == 'EE' ? 'eeTest/tests' : 'eoTest/tests'}/pending`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return data.map((test) => ({
    ...test,
    type: mode as any,
    examen: examen.toLowerCase() as any,
  }));
}

export const CorrectionList = async ({
  mode,
  examen,
}: {
  examen: string;
  mode: string;
}) => {
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';
  const data =
    session?.user.role! == 'admin' ? await getTests(token, mode, examen) : [];
  return <ExpressionList data={data} />;
};

export const ExpressionLoader = () => {
  return <Skeleton className="h-[100px] w-full rounded-md" />;
};
